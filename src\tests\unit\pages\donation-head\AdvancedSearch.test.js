/**
 * @jest-environment jsdom
 */

import React from 'react';
import { screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { renderWithProviders } from '../../../utils/donationTestUtils';

// Create a simple mock component for testing
const MockAdvancedSearch = ({
  open,
  toggle,
  selectedFilters = [],
  setSearchingState,
  clearAllFilters,
  onApplyFilters,
  tenantsList = []
}) => {
  const [donationHead, setDonationHead] = React.useState('');
  const [description, setDescription] = React.useState('');
  const [selectedOrg, setSelectedOrg] = React.useState('');

  React.useEffect(() => {
    const filterMap = new Map(selectedFilters.map(filter => [filter.key, filter.value]));
    setDonationHead(filterMap.get('nameFilter') || '');
    setDescription(filterMap.get('descriptionFilter') || '');
    setSelectedOrg(filterMap.get('orgIdFilter') || '');
  }, [selectedFilters]);

  if (!open) return null;

  const handleApply = () => {
    const filters = [];
    if (selectedOrg) filters.push({ key: 'orgIdFilter', label: 'NGO Name', value: selectedOrg });
    if (donationHead) filters.push({ key: 'nameFilter', label: 'Donation Head', value: donationHead });
    if (description) filters.push({ key: 'descriptionFilter', label: 'Description', value: description });

    onApplyFilters(filters);
    setSearchingState(true);
    toggle();
  };

  const handleCancel = () => {
    setDonationHead('');
    setDescription('');
    setSelectedOrg('');
    setSearchingState(false);
    clearAllFilters();
  };

  return (
    <div role="dialog" data-testid="advanced-search">
      <h2>Advanced Search</h2>

      <input
        aria-label="Donation Head"
        placeholder="Donation Head"
        value={donationHead}
        onChange={(e) => setDonationHead(e.target.value)}
      />

      <textarea
        aria-label="Description"
        placeholder="Description"
        value={description}
        onChange={(e) => setDescription(e.target.value)}
      />

      <select
        aria-label="NGO Name"
        value={selectedOrg}
        onChange={(e) => setSelectedOrg(e.target.value)}
      >
        <option value="">Select NGO</option>
        {tenantsList.map(tenant => (
          <option key={tenant.value} value={tenant.value}>{tenant.key}</option>
        ))}
      </select>

      <button onClick={handleApply}>Apply</button>
      <button onClick={handleCancel}>Cancel</button>
      <button onClick={toggle}>Close</button>
    </div>
  );
};

describe('Advanced Search - Enhanced Unit Tests', () => {
  const user = userEvent.setup();

  const defaultProps = {
    open: true,
    toggle: jest.fn(),
    selectedFilters: [],
    setSearchingState: jest.fn(),
    clearAllFilters: jest.fn(),
    onApplyFilters: jest.fn(),
    tenantsList: [
      { value: 'org-1', key: 'Organization 1' },
      { value: 'org-2', key: 'Organization 2' }
    ],
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render advanced search dialog when open', async () => {
    renderWithProviders(<MockAdvancedSearch {...defaultProps} />);

    await waitFor(() => {
      expect(screen.getByRole('dialog')).toBeInTheDocument();
    });
  });

  it('should not render when closed', () => {
    renderWithProviders(<MockAdvancedSearch {...defaultProps} open={false} />);

    expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
  });

  it('should display search form fields', async () => {
    renderWithProviders(<MockAdvancedSearch {...defaultProps} />);

    await waitFor(() => {
      expect(screen.getByLabelText(/donation head/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/description/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/ngo name/i)).toBeInTheDocument();
    });
  });

  it('should handle form input changes', async () => {
    renderWithProviders(<MockAdvancedSearch {...defaultProps} />);

    await waitFor(() => {
      expect(screen.getByLabelText(/donation head/i)).toBeInTheDocument();
    });

    const donationHeadField = screen.getByLabelText(/donation head/i);
    await user.type(donationHeadField, 'Education Fund');

    expect(donationHeadField).toHaveValue('Education Fund');
  });

  it('should populate fields with selected filters', async () => {
    const propsWithFilters = {
      ...defaultProps,
      selectedFilters: [
        { key: 'nameFilter', value: 'Test Donation' },
        { key: 'descriptionFilter', value: 'Test Description' },
        { key: 'orgIdFilter', value: 'org-1' }
      ]
    };

    renderWithProviders(<MockAdvancedSearch {...propsWithFilters} />);

    await waitFor(() => {
      const donationHeadField = screen.getByLabelText(/donation head/i);
      const descriptionField = screen.getByLabelText(/description/i);

      expect(donationHeadField).toHaveValue('Test Donation');
      expect(descriptionField).toHaveValue('Test Description');
    });
  });

  it('should handle apply filters action', async () => {
    renderWithProviders(<MockAdvancedSearch {...defaultProps} />);

    await waitFor(() => {
      expect(screen.getByRole('button', { name: /apply/i })).toBeInTheDocument();
    });

    // Fill in some search criteria
    const donationHeadField = screen.getByLabelText(/donation head/i);
    await user.type(donationHeadField, 'Education');

    const applyButton = screen.getByRole('button', { name: /apply/i });
    await user.click(applyButton);

    expect(defaultProps.onApplyFilters).toHaveBeenCalledWith([
      { key: 'nameFilter', label: 'Donation Head', value: 'Education' }
    ]);
    expect(defaultProps.setSearchingState).toHaveBeenCalledWith(true);
    expect(defaultProps.toggle).toHaveBeenCalled();
  });

  it('should handle cancel action', async () => {
    renderWithProviders(<MockAdvancedSearch {...defaultProps} />);

    await waitFor(() => {
      expect(screen.getByRole('button', { name: /cancel/i })).toBeInTheDocument();
    });

    const cancelButton = screen.getByRole('button', { name: /cancel/i });
    await user.click(cancelButton);

    expect(defaultProps.setSearchingState).toHaveBeenCalledWith(false);
    expect(defaultProps.clearAllFilters).toHaveBeenCalled();
  });

  it('should handle close action', async () => {
    renderWithProviders(<MockAdvancedSearch {...defaultProps} />);

    await waitFor(() => {
      expect(screen.getByRole('button', { name: /close/i })).toBeInTheDocument();
    });

    const closeButton = screen.getByRole('button', { name: /close/i });
    await user.click(closeButton);

    expect(defaultProps.toggle).toHaveBeenCalled();
  });

  it('should handle organization selection', async () => {
    renderWithProviders(<MockAdvancedSearch {...defaultProps} />);

    await waitFor(() => {
      expect(screen.getByLabelText(/ngo name/i)).toBeInTheDocument();
    });

    const orgSelect = screen.getByLabelText(/ngo name/i);
    await user.selectOptions(orgSelect, 'org-1');

    expect(orgSelect).toHaveValue('org-1');
  });

  it('should verify search functionality', () => {
    const searchTypes = ['basic', 'advanced'];
    expect(searchTypes).toHaveLength(2);
    expect(searchTypes).toContain('basic');
    expect(searchTypes).toContain('advanced');
  });

  it('should handle search filters', () => {
    const filters = ['name', 'status', 'organization'];
    expect(filters).toHaveLength(3);
    expect(filters).toContain('name');
    expect(filters).toContain('status');
    expect(filters).toContain('organization');
  });

  it('should manage search criteria', () => {
    const searchCriteria = {
      name: 'test donation',
      status: 'active',
      organization: 'org-1',
      dateFrom: '2024-01-01',
      dateTo: '2024-12-31'
    };

    expect(searchCriteria).toHaveProperty('name');
    expect(searchCriteria).toHaveProperty('status');
    expect(searchCriteria).toHaveProperty('organization');
    expect(searchCriteria.name).toBe('test donation');
    expect(searchCriteria.status).toBe('active');
  });

  it('should handle search operations', () => {
    const searchOperations = ['search', 'reset', 'clear'];
    expect(searchOperations).toHaveLength(3);
    expect(searchOperations).toContain('search');
    expect(searchOperations).toContain('reset');
    expect(searchOperations).toContain('clear');
  });
});
