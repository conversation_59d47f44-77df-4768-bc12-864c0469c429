/**
 * @jest-environment jsdom
 */

import React from 'react';
import { screen, waitFor, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { renderWithProviders, mockDonationHead, mockTenant } from '../../utils/donationTestUtils';

// Mock the complete donation head workflow components
const MockDonationHeadWorkflow = () => {
  const [donationHeads, setDonationHeads] = React.useState([
    mockDonationHead({ id: '1', name: 'Education Fund', description: 'Education support fund' }),
    mockDonationHead({ id: '2', name: 'Healthcare Aid', description: 'Healthcare assistance program', isActive: false }),
  ]);
  const [dialogOpen, setDialogOpen] = React.useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = React.useState(false);
  const [advancedSearchOpen, setAdvancedSearchOpen] = React.useState(false);
  const [selectedDonationHead, setSelectedDonationHead] = React.useState(null);
  const [searchValue, setSearchValue] = React.useState('');
  const [filters, setFilters] = React.useState([]);

  const tenantsList = [
    mockTenant({ value: 'org-1', key: 'Organization 1' }),
    mockTenant({ value: 'org-2', key: 'Organization 2' }),
  ];

  const handleCreate = (data) => {
    const newDonationHead = mockDonationHead({
      id: Date.now().toString(),
      name: data.name,
      description: data.description,
      orgId: data.orgId,
    });
    setDonationHeads(prev => [...prev, newDonationHead]);
    setDialogOpen(false);
  };

  const handleEdit = (data) => {
    setDonationHeads(prev => 
      prev.map(item => 
        item.id === selectedDonationHead.id 
          ? { ...item, name: data.name, description: data.description, orgId: data.orgId }
          : item
      )
    );
    setDialogOpen(false);
    setSelectedDonationHead(null);
  };

  const handleDelete = () => {
    if (selectedDonationHead) {
      setDonationHeads(prev => 
        prev.map(item => 
          item.id === selectedDonationHead.id 
            ? { ...item, isActive: !item.isActive }
            : item
        )
      );
    }
    setDeleteDialogOpen(false);
    setSelectedDonationHead(null);
  };

  const handleApplyFilters = (appliedFilters) => {
    setFilters(appliedFilters);
    setAdvancedSearchOpen(false);
  };

  const filteredDonationHeads = donationHeads.filter(item => {
    if (searchValue && !item.name.toLowerCase().includes(searchValue.toLowerCase())) {
      return false;
    }
    
    for (const filter of filters) {
      if (filter.key === 'nameFilter' && !item.name.toLowerCase().includes(filter.value.toLowerCase())) {
        return false;
      }
      if (filter.key === 'descriptionFilter' && !item.description.toLowerCase().includes(filter.value.toLowerCase())) {
        return false;
      }
      if (filter.key === 'orgIdFilter' && item.orgId !== filter.value) {
        return false;
      }
    }
    
    return true;
  });

  return (
    <div>
      <h1>Donation Head Management</h1>
      
      {/* Search and Actions */}
      <div data-testid="search-section">
        <input
          type="text"
          placeholder="Search donation heads..."
          value={searchValue}
          onChange={(e) => setSearchValue(e.target.value)}
          data-testid="search-input"
        />
        <button 
          onClick={() => setAdvancedSearchOpen(true)}
          data-testid="advanced-search-button"
        >
          Advanced Search
        </button>
        <button 
          onClick={() => {
            setSelectedDonationHead(null);
            setDialogOpen(true);
          }}
          data-testid="add-button"
        >
          Add Donation Head
        </button>
      </div>

      {/* Applied Filters */}
      {filters.length > 0 && (
        <div data-testid="applied-filters">
          <h3>Applied Filters:</h3>
          {filters.map((filter, index) => (
            <span key={index} data-testid={`filter-${filter.key}`}>
              {filter.label}: {filter.value}
            </span>
          ))}
          <button onClick={() => setFilters([])} data-testid="clear-filters">
            Clear All Filters
          </button>
        </div>
      )}

      {/* Data Grid */}
      <div data-testid="data-grid">
        <table>
          <thead>
            <tr>
              <th>Name</th>
              <th>Description</th>
              <th>Status</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {filteredDonationHeads.map(item => (
              <tr key={item.id} data-testid={`row-${item.id}`}>
                <td>{item.name}</td>
                <td>{item.description}</td>
                <td>{item.isActive ? 'Active' : 'Inactive'}</td>
                <td>
                  <button 
                    onClick={() => {
                      setSelectedDonationHead(item);
                      setDialogOpen(true);
                    }}
                    data-testid={`edit-${item.id}`}
                  >
                    Edit
                  </button>
                  <button 
                    onClick={() => {
                      setSelectedDonationHead(item);
                      setDeleteDialogOpen(true);
                    }}
                    data-testid={`toggle-${item.id}`}
                  >
                    {item.isActive ? 'Deactivate' : 'Activate'}
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Create/Edit Dialog */}
      {dialogOpen && (
        <MockDonationHeadDialog
          open={dialogOpen}
          onClose={() => {
            setDialogOpen(false);
            setSelectedDonationHead(null);
          }}
          formData={selectedDonationHead}
          tenantsList={tenantsList}
          onSave={selectedDonationHead ? handleEdit : handleCreate}
        />
      )}

      {/* Delete Dialog */}
      {deleteDialogOpen && (
        <MockDeleteDialog
          open={deleteDialogOpen}
          onClose={() => {
            setDeleteDialogOpen(false);
            setSelectedDonationHead(null);
          }}
          data={selectedDonationHead}
          onConfirm={handleDelete}
        />
      )}

      {/* Advanced Search */}
      {advancedSearchOpen && (
        <MockAdvancedSearch
          open={advancedSearchOpen}
          onClose={() => setAdvancedSearchOpen(false)}
          tenantsList={tenantsList}
          onApplyFilters={handleApplyFilters}
        />
      )}
    </div>
  );
};

// Mock Dialog Components
const MockDonationHeadDialog = ({ open, onClose, formData, tenantsList, onSave }) => {
  const [name, setName] = React.useState(formData?.name || '');
  const [description, setDescription] = React.useState(formData?.description || '');
  const [orgId, setOrgId] = React.useState(formData?.orgId || '');

  React.useEffect(() => {
    setName(formData?.name || '');
    setDescription(formData?.description || '');
    setOrgId(formData?.orgId || '');
  }, [formData]);

  const handleSave = () => {
    onSave({ name, description, orgId });
  };

  if (!open) return null;

  return (
    <div role="dialog" data-testid="donation-head-dialog">
      <h2>{formData?.id ? 'Edit' : 'Create'} Donation Head</h2>
      
      <input
        aria-label="Donation Head Name"
        placeholder="Donation Head Name"
        value={name}
        onChange={(e) => setName(e.target.value)}
        data-testid="name-input"
      />

      <textarea
        aria-label="Description"
        placeholder="Description"
        value={description}
        onChange={(e) => setDescription(e.target.value)}
        data-testid="description-input"
      />

      <select
        aria-label="NGO Name"
        value={orgId}
        onChange={(e) => setOrgId(e.target.value)}
        data-testid="org-select"
      >
        <option value="">Select NGO</option>
        {tenantsList.map(tenant => (
          <option key={tenant.value} value={tenant.value}>{tenant.key}</option>
        ))}
      </select>

      <button onClick={handleSave} data-testid="save-button">Save</button>
      <button onClick={onClose} data-testid="cancel-button">Cancel</button>
    </div>
  );
};

const MockDeleteDialog = ({ open, onClose, data, onConfirm }) => {
  if (!open) return null;

  return (
    <div role="dialog" data-testid="delete-dialog">
      <h2>Confirm Action</h2>
      <p>
        Are you sure you want to {data?.isActive ? 'deactivate' : 'activate'} {data?.name}?
      </p>
      <button onClick={onConfirm} data-testid="confirm-button">
        {data?.isActive ? 'Deactivate' : 'Activate'}
      </button>
      <button onClick={onClose} data-testid="cancel-delete-button">Cancel</button>
    </div>
  );
};

const MockAdvancedSearch = ({ open, onClose, tenantsList, onApplyFilters }) => {
  const [name, setName] = React.useState('');
  const [description, setDescription] = React.useState('');
  const [orgId, setOrgId] = React.useState('');

  const handleApply = () => {
    const filters = [];
    if (name) filters.push({ key: 'nameFilter', label: 'Name', value: name });
    if (description) filters.push({ key: 'descriptionFilter', label: 'Description', value: description });
    if (orgId) filters.push({ key: 'orgIdFilter', label: 'Organization', value: orgId });
    
    onApplyFilters(filters);
  };

  if (!open) return null;

  return (
    <div role="dialog" data-testid="advanced-search-dialog">
      <h2>Advanced Search</h2>
      
      <input
        aria-label="Filter by Name"
        placeholder="Filter by Name"
        value={name}
        onChange={(e) => setName(e.target.value)}
        data-testid="filter-name-input"
      />

      <textarea
        aria-label="Filter by Description"
        placeholder="Filter by Description"
        value={description}
        onChange={(e) => setDescription(e.target.value)}
        data-testid="filter-description-input"
      />

      <select
        aria-label="Filter by Organization"
        value={orgId}
        onChange={(e) => setOrgId(e.target.value)}
        data-testid="filter-org-select"
      >
        <option value="">Select Organization</option>
        {tenantsList.map(tenant => (
          <option key={tenant.value} value={tenant.value}>{tenant.key}</option>
        ))}
      </select>

      <button onClick={handleApply} data-testid="apply-filters-button">Apply Filters</button>
      <button onClick={onClose} data-testid="close-search-button">Close</button>
    </div>
  );
};

describe('Donation Head Workflow - Integration Tests', () => {
  const user = userEvent.setup();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render the complete donation head management interface', async () => {
    renderWithProviders(<MockDonationHeadWorkflow />);

    await waitFor(() => {
      expect(screen.getByText('Donation Head Management')).toBeInTheDocument();
      expect(screen.getByTestId('search-input')).toBeInTheDocument();
      expect(screen.getByTestId('add-button')).toBeInTheDocument();
      expect(screen.getByTestId('data-grid')).toBeInTheDocument();
    });
  });

  it('should perform complete create workflow', async () => {
    renderWithProviders(<MockDonationHeadWorkflow />);

    // Click Add button
    const addButton = screen.getByTestId('add-button');
    await user.click(addButton);

    // Verify dialog opens
    await waitFor(() => {
      expect(screen.getByTestId('donation-head-dialog')).toBeInTheDocument();
      expect(screen.getByText('Create Donation Head')).toBeInTheDocument();
    });

    // Fill form
    const nameInput = screen.getByTestId('name-input');
    const descriptionInput = screen.getByTestId('description-input');
    const orgSelect = screen.getByTestId('org-select');

    await user.type(nameInput, 'New Donation Head');
    await user.type(descriptionInput, 'New description');
    await user.selectOptions(orgSelect, 'org-1');

    // Save
    const saveButton = screen.getByTestId('save-button');
    await user.click(saveButton);

    // Verify dialog closes and new item appears
    await waitFor(() => {
      expect(screen.queryByTestId('donation-head-dialog')).not.toBeInTheDocument();
    });

    // Check if new item is in the grid
    await waitFor(() => {
      expect(screen.getByText('New Donation Head')).toBeInTheDocument();
      expect(screen.getByText('New description')).toBeInTheDocument();
    });
  });

  it('should perform complete edit workflow', async () => {
    renderWithProviders(<MockDonationHeadWorkflow />);

    // Click edit button for first item
    const editButton = screen.getByTestId('edit-1');
    await user.click(editButton);

    // Verify dialog opens with existing data
    await waitFor(() => {
      expect(screen.getByTestId('donation-head-dialog')).toBeInTheDocument();
      expect(screen.getByText('Edit Donation Head')).toBeInTheDocument();
    });

    // Verify form is pre-filled
    const nameInput = screen.getByTestId('name-input');
    const descriptionInput = screen.getByTestId('description-input');

    expect(nameInput).toHaveValue('Education Fund');
    expect(descriptionInput).toHaveValue('Education support fund');

    // Modify data
    await user.clear(nameInput);
    await user.type(nameInput, 'Updated Education Fund');
    await user.clear(descriptionInput);
    await user.type(descriptionInput, 'Updated description');

    // Save changes
    const saveButton = screen.getByTestId('save-button');
    await user.click(saveButton);

    // Verify changes are reflected
    await waitFor(() => {
      expect(screen.queryByTestId('donation-head-dialog')).not.toBeInTheDocument();
    });

    await waitFor(() => {
      expect(screen.getByText('Updated Education Fund')).toBeInTheDocument();
      expect(screen.getByText('Updated description')).toBeInTheDocument();
    });
  });

  it('should perform complete delete/toggle workflow', async () => {
    renderWithProviders(<MockDonationHeadWorkflow />);

    // Verify initial state
    const row1 = screen.getByTestId('row-1');
    expect(within(row1).getByText('Active')).toBeInTheDocument();

    // Click toggle button
    const toggleButton = screen.getByTestId('toggle-1');
    await user.click(toggleButton);

    // Verify delete dialog opens
    await waitFor(() => {
      expect(screen.getByTestId('delete-dialog')).toBeInTheDocument();
      expect(screen.getByText(/deactivate.*Education Fund/)).toBeInTheDocument();
    });

    // Confirm action
    const confirmButton = screen.getByTestId('confirm-button');
    await user.click(confirmButton);

    // Verify status change
    await waitFor(() => {
      expect(screen.queryByTestId('delete-dialog')).not.toBeInTheDocument();
    });

    await waitFor(() => {
      const updatedRow = screen.getByTestId('row-1');
      expect(within(updatedRow).getByText('Inactive')).toBeInTheDocument();
    });
  });

  it('should perform search functionality', async () => {
    renderWithProviders(<MockDonationHeadWorkflow />);

    // Verify initial state - both items visible
    expect(screen.getByText('Education Fund')).toBeInTheDocument();
    expect(screen.getByText('Healthcare Aid')).toBeInTheDocument();

    // Search for specific item
    const searchInput = screen.getByTestId('search-input');
    await user.type(searchInput, 'Education');

    // Verify filtered results
    await waitFor(() => {
      expect(screen.getByText('Education Fund')).toBeInTheDocument();
      expect(screen.queryByText('Healthcare Aid')).not.toBeInTheDocument();
    });

    // Clear search
    await user.clear(searchInput);

    // Verify all items are visible again
    await waitFor(() => {
      expect(screen.getByText('Education Fund')).toBeInTheDocument();
      expect(screen.getByText('Healthcare Aid')).toBeInTheDocument();
    });
  });

  it('should perform advanced search workflow', async () => {
    renderWithProviders(<MockDonationHeadWorkflow />);

    // Open advanced search
    const advancedSearchButton = screen.getByTestId('advanced-search-button');
    await user.click(advancedSearchButton);

    // Verify advanced search dialog opens
    await waitFor(() => {
      expect(screen.getByTestId('advanced-search-dialog')).toBeInTheDocument();
    });

    // Fill filter criteria
    const filterNameInput = screen.getByTestId('filter-name-input');
    await user.type(filterNameInput, 'Education');

    // Apply filters
    const applyButton = screen.getByTestId('apply-filters-button');
    await user.click(applyButton);

    // Verify dialog closes and filters are applied
    await waitFor(() => {
      expect(screen.queryByTestId('advanced-search-dialog')).not.toBeInTheDocument();
    });

    // Verify applied filters section appears
    await waitFor(() => {
      expect(screen.getByTestId('applied-filters')).toBeInTheDocument();
      expect(screen.getByTestId('filter-nameFilter')).toBeInTheDocument();
    });

    // Verify filtered results
    expect(screen.getByText('Education Fund')).toBeInTheDocument();
    expect(screen.queryByText('Healthcare Aid')).not.toBeInTheDocument();

    // Clear filters
    const clearFiltersButton = screen.getByTestId('clear-filters');
    await user.click(clearFiltersButton);

    // Verify all items are visible again
    await waitFor(() => {
      expect(screen.queryByTestId('applied-filters')).not.toBeInTheDocument();
      expect(screen.getByText('Education Fund')).toBeInTheDocument();
      expect(screen.getByText('Healthcare Aid')).toBeInTheDocument();
    });
  });

  it('should handle dialog cancellation workflows', async () => {
    renderWithProviders(<MockDonationHeadWorkflow />);

    // Test create dialog cancellation
    const addButton = screen.getByTestId('add-button');
    await user.click(addButton);

    await waitFor(() => {
      expect(screen.getByTestId('donation-head-dialog')).toBeInTheDocument();
    });

    const cancelButton = screen.getByTestId('cancel-button');
    await user.click(cancelButton);

    await waitFor(() => {
      expect(screen.queryByTestId('donation-head-dialog')).not.toBeInTheDocument();
    });

    // Test delete dialog cancellation
    const toggleButton = screen.getByTestId('toggle-1');
    await user.click(toggleButton);

    await waitFor(() => {
      expect(screen.getByTestId('delete-dialog')).toBeInTheDocument();
    });

    const cancelDeleteButton = screen.getByTestId('cancel-delete-button');
    await user.click(cancelDeleteButton);

    await waitFor(() => {
      expect(screen.queryByTestId('delete-dialog')).not.toBeInTheDocument();
    });

    // Verify no changes were made
    const row1 = screen.getByTestId('row-1');
    expect(within(row1).getByText('Active')).toBeInTheDocument();
  });

  it('should handle complex filter combinations', async () => {
    renderWithProviders(<MockDonationHeadWorkflow />);

    // Open advanced search
    const advancedSearchButton = screen.getByTestId('advanced-search-button');
    await user.click(advancedSearchButton);

    await waitFor(() => {
      expect(screen.getByTestId('advanced-search-dialog')).toBeInTheDocument();
    });

    // Apply multiple filters
    const filterNameInput = screen.getByTestId('filter-name-input');
    const filterDescriptionInput = screen.getByTestId('filter-description-input');
    const filterOrgSelect = screen.getByTestId('filter-org-select');

    await user.type(filterNameInput, 'Fund');
    await user.type(filterDescriptionInput, 'support');
    await user.selectOptions(filterOrgSelect, 'org-1');

    const applyButton = screen.getByTestId('apply-filters-button');
    await user.click(applyButton);

    // Verify multiple filters are applied
    await waitFor(() => {
      expect(screen.getByTestId('applied-filters')).toBeInTheDocument();
      expect(screen.getByTestId('filter-nameFilter')).toBeInTheDocument();
      expect(screen.getByTestId('filter-descriptionFilter')).toBeInTheDocument();
      expect(screen.getByTestId('filter-orgIdFilter')).toBeInTheDocument();
    });
  });

  it('should maintain data consistency across operations', async () => {
    renderWithProviders(<MockDonationHeadWorkflow />);

    // Create new item
    const addButton = screen.getByTestId('add-button');
    await user.click(addButton);

    await waitFor(() => {
      expect(screen.getByTestId('donation-head-dialog')).toBeInTheDocument();
    });

    const nameInput = screen.getByTestId('name-input');
    const descriptionInput = screen.getByTestId('description-input');

    await user.type(nameInput, 'Test Fund');
    await user.type(descriptionInput, 'Test description');

    const saveButton = screen.getByTestId('save-button');
    await user.click(saveButton);

    // Verify item is created
    await waitFor(() => {
      expect(screen.getByText('Test Fund')).toBeInTheDocument();
    });

    // Search for the new item
    const searchInput = screen.getByTestId('search-input');
    await user.type(searchInput, 'Test');

    await waitFor(() => {
      expect(screen.getByText('Test Fund')).toBeInTheDocument();
      expect(screen.queryByText('Education Fund')).not.toBeInTheDocument();
    });

    // Clear search and verify all items are still there
    await user.clear(searchInput);

    await waitFor(() => {
      expect(screen.getByText('Test Fund')).toBeInTheDocument();
      expect(screen.getByText('Education Fund')).toBeInTheDocument();
      expect(screen.getByText('Healthcare Aid')).toBeInTheDocument();
    });
  });
});
